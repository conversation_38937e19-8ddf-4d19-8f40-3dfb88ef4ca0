//! 线性路由引擎
//!
//! 实现线性路由模式：A -> B -> C -> D
//! 支持多步骤跳转的直线交换

use anchor_lang::prelude::*;
use crate::constants::{RouteConfig, RoutingMode};
use crate::{distribute_swap, Dex, Route};
use crate::error::RouteError;
use crate::state::event::{RouteExecuted, route_phases};

/// 线性路由执行器
pub struct LinearRouteExecutor;

impl LinearRouteExecutor {
    /// 执行线性路由
    pub fn execute_linear_route<'info>(
        config: &RouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
    ) -> Result<u64> {
        // 使用增强的验证逻辑
        Self::validate_linear_config(config)?;

        // 发出路由开始事件
        emit!(RouteExecuted {
            phase: route_phases::STARTED,
            mode: config.mode.clone(),
            user: None,
            amount_in: config.amount_in,
            amount_out: 0,
            min_amount_out: config.min_amount_out,
            routes_count: config.routes.len() as u8,
            routes_executed: 0,
            total_fees: 0,
            execution_time: 0,
            success: false,
            actual_slippage_bps: 0,
            timestamp: Clock::get()?.unix_timestamp,
        });

        let mut current_amount = config.amount_in;

        // 逐步执行每个路由
        let mut offset: usize = 0;
        for (step, route) in config.routes.iter().enumerate() {
            msg!("执行线性路由步骤 {}/{}: {:?} -> {:?}",
                step + 1,
                config.routes.len(),
                route.input_mint,
                route.output_mint
            );


            // 执行单步交换
            let amount_out = distribute_swap(
                &route.dex,
                remaining_accounts,
                current_amount,
                &mut offset,
                route.input_mint,
                route.output_mint,
            )?;

            // 更新状态
            current_amount = amount_out;
        }

        // 验证最终输出是否满足最小要求
        require!(
            current_amount >= config.min_amount_out,
            RouteError::InsufficientOutput
        );

        Ok(current_amount)
    }


    /// 验证线性路由配置
    pub fn validate_linear_config(config: &RouteConfig) -> Result<()> {
        require!(
            config.mode == RoutingMode::Linear,
            RouteError::InvalidRouteConfig
        );

        require!(
            !config.routes.is_empty(),
            RouteError::EmptyRoute
        );

        require!(
            config.routes.len() <= 6,
            RouteError::TooManyRouteSteps
        );

        require!(
            config.amount_in > 0,
            RouteError::ZeroAmount
        );

        require!(
            config.min_amount_out > 0,
            RouteError::ZeroAmount
        );

        // 增强的路由连续性验证
        Self::validate_route_continuity(&config.routes)?;

        Ok(())
    }

    /// 增强的路由连续性验证
    /// 验证相邻路由步骤之间的连续性
    pub fn validate_route_continuity(routes: &[Route]) -> Result<()> {
        if routes.is_empty() {
            return Ok(());
        }

        for i in 1..routes.len() {
            let prev_route = &routes[i - 1];
            let current_route = &routes[i];

            // 1. 基本的mint连续性验证
            if prev_route.output_mint != current_route.input_mint {
                msg!(
                    "路由连续性失败：步骤{}的输出({}) 与 步骤{}的输入({}) 不匹配",
                    i - 1,
                    prev_route.output_mint,
                    i,
                    current_route.input_mint
                );
                return Err(RouteError::RouteDiscontinuity.into());
            }

            // 2. DEX类型合理性验证
            Self::validate_dex_compatibility(current_route.dex, i)?;
        }

        msg!("路由连续性验证通过：{}个步骤", routes.len());
        Ok(())
    }

    /// 验证DEX兼容性
    fn validate_dex_compatibility(dex: Dex, step: usize) -> Result<()> {
        match dex {
            Dex::RaydiumClmm |
            Dex::RaydiumCpmm => {
                // Raydium DEX一般都可用
                Ok(())
            },
            Dex::MeteoraDlmm |
            Dex::MeteoraAmm => {
                // TODO: 添加Meteora的特定验证逻辑
                msg!("步骤{}: 使用Meteora DEX，注意查验流动性", step);
                Ok(())
            },
            Dex::PumpSwap => {
                // PumpSwap只支持特定的代币对
                msg!("步骤{}: 使用PumpSwap，确保是支持的代币对", step);
                Ok(())
            },
            Dex::Orca => {
                // Orca通常可用
                msg!("步骤{}: 使用Orca DEX", step);
                Ok(())
            },
        }
    }
}



/// 线性路由统计信息
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct LinearRouteStats {
    pub steps: u8,
    pub total_fee_bps: u16,
    pub estimated_output: u64,
    pub price_impact_bps: u16,
    pub execution_time_estimate: u32, // 秒
}

impl LinearRouteStats {
    /// 计算线性路由统计
    pub fn calculate(config: &RouteConfig) -> Result<Self> {
        LinearRouteExecutor::validate_linear_config(config)?;

        let steps = config.routes.len() as u8;

        // 计算总费用
        let mut total_fee_bps = 0u16;
        for _route in &config.routes {
            // TODO: 根据不同的DEX类型计算实际费用
            let fee_bps = 0; // 占位符值，后续实现
            total_fee_bps = total_fee_bps.checked_add(fee_bps)
                .ok_or(RouteError::MathOverflow)?;
        }

        // 估算执行时间（每步约2秒）
        let execution_time_estimate = (steps as u32) * 2;

        // 简化的价格影响估算
        let price_impact_bps = (steps as u16) * 50; // 每步约0.5%价格影响

        Ok(LinearRouteStats {
            steps,
            total_fee_bps,
            estimated_output: 0, // 需要额外数据计算
            price_impact_bps,
            execution_time_estimate,
        })
    }
}
