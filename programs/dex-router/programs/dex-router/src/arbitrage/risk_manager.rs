//! 套利风险管理器
//!
//! 评估和管理套利交易的风险

use anchor_lang::prelude::*;
use crate::constants::{RouteConfig, Dex};
use crate::error::RouteError;
use crate::state::event::{SecurityEvent, security_event_types, severity_levels};

/// 风险等级
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Copy, Debug, PartialEq)]
pub enum RiskLevel {
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3,
}

impl Default for RiskLevel {
    fn default() -> Self {
        Self::Medium
    }
}

/// 套利风险管理器
pub struct ArbitrageRiskManager;

impl ArbitrageRiskManager {
    /// 评估套利交易风险
    pub fn assess_arbitrage_risk(
        config: &RouteConfig,
        expected_profit: u64,
        market_conditions: &MarketConditions,
    ) -> Result<RiskAssessment> {
        let mut risk_factors = Vec::new();
        let mut total_score = 0u16;

        // 1. 金额风险评估 (0-25分)
        let amount_risk = Self::assess_amount_risk(config.amount_in);
        total_score += amount_risk.score;
        if amount_risk.score > 15 {
            risk_factors.push(amount_risk.description);
        }

        // 2. 流动性风险评估 (0-20分)
        let liquidity_risk = Self::assess_liquidity_risk(config, market_conditions);
        total_score += liquidity_risk.score;
        if liquidity_risk.score > 10 {
            risk_factors.push(liquidity_risk.description);
        }

        // 3. 价格影响风险评估 (0-20分)
        let price_impact_risk = Self::assess_price_impact_risk(config, market_conditions);
        total_score += price_impact_risk.score;
        if price_impact_risk.score > 12 {
            risk_factors.push(price_impact_risk.description);
        }

        // 4. DEX 可靠性风险评估 (0-15分)
        let dex_reliability_risk = Self::assess_dex_reliability_risk(config);
        total_score += dex_reliability_risk.score;
        if dex_reliability_risk.score > 8 {
            risk_factors.push(dex_reliability_risk.description);
        }

        // 5. 利润率风险评估 (0-20分)
        let profit_risk = Self::assess_profit_risk(config.amount_in, expected_profit);
        total_score += profit_risk.score;
        if profit_risk.score > 12 {
            risk_factors.push(profit_risk.description);
        }

        // 确定风险等级
        let risk_level = match total_score {
            0..=25 => RiskLevel::Low,
            26..=50 => RiskLevel::Medium,
            51..=75 => RiskLevel::High,
            _ => RiskLevel::Critical,
        };

        // 生成建议
        let recommendations = Self::generate_recommendations(&risk_level, &risk_factors, config);

        // 决定是否批准
        let approved = Self::should_approve_arbitrage(&risk_level, total_score, config);

        // 如果是高风险或关键风险，发出安全事件
        if risk_level == RiskLevel::High || risk_level == RiskLevel::Critical {
            let severity = match risk_level {
                RiskLevel::High => severity_levels::WARNING,
                RiskLevel::Critical => severity_levels::ERROR,
                _ => severity_levels::INFO,
            };

            emit!(SecurityEvent {
                event_type: security_event_types::RISK_ASSESSMENT,
                severity,
                user: None,
                dex: None,
                input_mint: Some(config.routes[0].input_mint),
                output_mint: Some(config.routes.last().unwrap().output_mint),
                amount: config.amount_in,
                max_allowed_amount: Self::calculate_max_safe_amount(config)?,
                score_or_impact_bps: total_score,
                approved,
                description: format!("套利风险评估: {} 级风险", 
                    match risk_level {
                        RiskLevel::Low => "低",
                        RiskLevel::Medium => "中",
                        RiskLevel::High => "高",
                        RiskLevel::Critical => "严重",
                    }
                ),
                timestamp: Clock::get()?.unix_timestamp,
            });
        }

        Ok(RiskAssessment {
            risk_level,
            total_score,
            risk_factors,
            recommendations,
            approved,
            max_recommended_amount: Self::calculate_max_safe_amount(config)?,
        })
    }

    /// 评估金额风险
    fn assess_amount_risk(amount: u64) -> RiskFactor {
        let (score, description) = match amount {
            0..=10_000_000 => (5, "金额适中".to_string()),
            10_000_001..=100_000_000 => (10, "金额较大，需要关注".to_string()),
            100_000_001..=1_000_000_000 => (18, "大额交易，高风险".to_string()),
            _ => (25, "超大额交易，极高风险".to_string()),
        };

        RiskFactor { score, description }
    }

    /// 评估流动性风险
    fn assess_liquidity_risk(config: &RouteConfig, market_conditions: &MarketConditions) -> RiskFactor {
        let mut score = 0u16;
        let mut issues = Vec::new();

        // 检查每个 DEX 的流动性
        for route in &config.routes {
            let dex_liquidity = market_conditions.get_dex_liquidity(&route.dex);
            
            if dex_liquidity < config.amount_in * 2 {
                score += 8;
                issues.push(format!("{:?} 流动性不足", route.dex));
            } else if dex_liquidity < config.amount_in * 5 {
                score += 4;
                issues.push(format!("{:?} 流动性紧张", route.dex));
            }
        }

        let description = if issues.is_empty() {
            "流动性充足".to_string()
        } else {
            format!("流动性风险: {}", issues.join(", "))
        };

        RiskFactor { score: score.min(20), description }
    }

    /// 评估价格影响风险
    fn assess_price_impact_risk(config: &RouteConfig, market_conditions: &MarketConditions) -> RiskFactor {
        let mut max_price_impact = 0u16;

        for route in &config.routes {
            let estimated_impact = market_conditions.estimate_price_impact(&route.dex, config.amount_in);
            max_price_impact = max_price_impact.max(estimated_impact);
        }

        let (score, description) = match max_price_impact {
            0..=100 => (2, "价格影响很小".to_string()),     // <1%
            101..=300 => (6, "价格影响适中".to_string()),   // 1-3%
            301..=500 => (12, "价格影响较大".to_string()),  // 3-5%
            501..=1000 => (16, "价格影响很大".to_string()), // 5-10%
            _ => (20, "价格影响极大，可能失败".to_string()),  // >10%
        };

        RiskFactor { score, description }
    }

    /// 评估 DEX 可靠性风险
    fn assess_dex_reliability_risk(config: &RouteConfig) -> RiskFactor {
        let mut total_score = 0u16;
        let mut risky_dexes = Vec::new();

        for route in &config.routes {
            let dex_score = match route.dex {
                Dex::RaydiumClmm | Dex::RaydiumCpmm => 2,  // 高可靠性
                Dex::Orca => 3,                             // 高可靠性
                Dex::MeteoraDlmm | Dex::MeteoraAmm => 4,    // 中等可靠性
                Dex::PumpSwap => 6,                         // 较低可靠性
            };

            total_score += dex_score;
            
            if dex_score > 4 {
                risky_dexes.push(format!("{:?}", route.dex));
            }
        }

        let description = if risky_dexes.is_empty() {
            "所有 DEX 可靠性良好".to_string()
        } else {
            format!("存在风险 DEX: {}", risky_dexes.join(", "))
        };

        RiskFactor { 
            score: total_score.min(15), 
            description 
        }
    }

    /// 评估利润率风险
    fn assess_profit_risk(amount_in: u64, expected_profit: u64) -> RiskFactor {
        let profit_bps = if amount_in > 0 {
            (expected_profit * 10000 / amount_in) as u16
        } else {
            0
        };

        let (score, description) = match profit_bps {
            0..=10 => (20, "利润率极低，可能亏损".to_string()),    // <0.1%
            11..=50 => (15, "利润率很低，风险较高".to_string()),   // 0.1-0.5%
            51..=100 => (8, "利润率偏低，需要谨慎".to_string()),   // 0.5-1%
            101..=300 => (3, "利润率合理".to_string()),          // 1-3%
            301..=500 => (1, "利润率良好".to_string()),          // 3-5%
            _ => (0, "利润率很高".to_string()),                  // >5%
        };

        RiskFactor { score, description }
    }

    /// 生成风险管理建议
    fn generate_recommendations(
        risk_level: &RiskLevel,
        risk_factors: &[String],
        config: &RouteConfig,
    ) -> Vec<String> {
        let mut recommendations = Vec::new();

        match risk_level {
            RiskLevel::Low => {
                recommendations.push("风险较低，可以正常执行".to_string());
            },
            RiskLevel::Medium => {
                recommendations.push("中等风险，建议监控执行过程".to_string());
                if config.amount_in > 50_000_000 {
                    recommendations.push("考虑降低交易金额".to_string());
                }
            },
            RiskLevel::High => {
                recommendations.push("高风险，建议降低交易金额".to_string());
                recommendations.push("增加滑点保护".to_string());
                if config.routes.len() > 3 {
                    recommendations.push("考虑简化路由路径".to_string());
                }
            },
            RiskLevel::Critical => {
                recommendations.push("极高风险，不建议执行".to_string());
                recommendations.push("等待市场条件改善".to_string());
                recommendations.push("考虑其他套利机会".to_string());
            },
        }

        // 基于具体风险因素的建议
        for factor in risk_factors {
            if factor.contains("流动性") {
                recommendations.push("等待流动性增加或选择其他 DEX".to_string());
            }
            if factor.contains("价格影响") {
                recommendations.push("分批执行或选择流动性更高的池子".to_string());
            }
        }

        recommendations
    }

    /// 决定是否批准套利交易
    fn should_approve_arbitrage(risk_level: &RiskLevel, total_score: u16, config: &RouteConfig) -> bool {
        match risk_level {
            RiskLevel::Low => true,
            RiskLevel::Medium => total_score < 40,
            RiskLevel::High => {
                // 高风险但金额不大的情况下可以批准
                total_score < 65 && config.amount_in < 10_000_000
            },
            RiskLevel::Critical => false,
        }
    }

    /// 计算最大安全金额
    fn calculate_max_safe_amount(config: &RouteConfig) -> Result<u64> {
        // 基于路由复杂度和 DEX 类型计算安全金额
        let base_amount = 100_000_000u64; // 基准金额

        let complexity_factor = match config.routes.len() {
            1 => 1.0,
            2 => 0.8,
            3 => 0.6,
            4 => 0.4,
            _ => 0.2,
        };

        let dex_factor = config.routes.iter()
            .map(|route| match route.dex {
                Dex::RaydiumClmm | Dex::RaydiumCpmm | Dex::Orca => 1.0,
                Dex::MeteoraDlmm | Dex::MeteoraAmm => 0.8,
                Dex::PumpSwap => 0.6,
            })
            .fold(1.0, |acc, factor| acc * factor);

        let safe_amount = (base_amount as f64 * complexity_factor * dex_factor) as u64;
        Ok(safe_amount)
    }

    /// 实时监控套利执行
    pub fn monitor_arbitrage_execution(
        initial_assessment: &RiskAssessment,
        current_step: u8,
        total_steps: u8,
        intermediate_results: &[u64],
    ) -> Result<MonitoringResult> {
        let progress_ratio = current_step as f64 / total_steps as f64;
        
        // 检查是否偏离预期
        let mut warnings = Vec::new();
        let mut should_abort = false;

        // 如果已经执行了一半以上，检查中间结果
        if progress_ratio > 0.5 && !intermediate_results.is_empty() {
            let expected_mid_output = intermediate_results[0] * current_step as u64 / total_steps as u64;
            let actual_output = intermediate_results.last().copied().unwrap_or(0);
            
            if actual_output < expected_mid_output * 80 / 100 { // 低于预期80%
                warnings.push("执行结果低于预期，可能存在滑点过大".to_string());
                
                if initial_assessment.risk_level == RiskLevel::High {
                    should_abort = true;
                    warnings.push("高风险交易结果不理想，建议中止".to_string());
                }
            }
        }

        let warnings_empty = warnings.is_empty();
        
        Ok(MonitoringResult {
            should_continue: !should_abort,
            warnings,
            recommended_action: if should_abort {
                RecommendedAction::Abort
            } else if !warnings_empty {
                RecommendedAction::Proceed_With_Caution
            } else {
                RecommendedAction::Proceed
            },
        })
    }
}

/// 市场条件信息
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct MarketConditions {
    pub raydium_liquidity: u64,
    pub orca_liquidity: u64,
    pub meteora_liquidity: u64,
    pub pumpswap_liquidity: u64,
    pub volatility_index: u16, // 基点
}

impl MarketConditions {
    pub fn get_dex_liquidity(&self, dex: &Dex) -> u64 {
        match dex {
            Dex::RaydiumClmm | Dex::RaydiumCpmm => self.raydium_liquidity,
            Dex::Orca => self.orca_liquidity,
            Dex::MeteoraDlmm | Dex::MeteoraAmm => self.meteora_liquidity,
            Dex::PumpSwap => self.pumpswap_liquidity,
        }
    }

    pub fn estimate_price_impact(&self, dex: &Dex, amount: u64) -> u16 {
        let liquidity = self.get_dex_liquidity(dex);
        if liquidity == 0 {
            return 10000; // 100% 影响（无流动性）
        }

        // 简化的价格影响模型
        let impact_ratio = (amount * 10000 / liquidity) as u16;
        (impact_ratio / 10).min(1000) // 最大10%影响
    }
}

/// 风险因素
#[derive(Clone, Debug)]
pub struct RiskFactor {
    pub score: u16,
    pub description: String,
}

/// 风险评估结果
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RiskAssessment {
    pub risk_level: RiskLevel,
    pub total_score: u16,
    pub risk_factors: Vec<String>,
    pub recommendations: Vec<String>,
    pub approved: bool,
    pub max_recommended_amount: u64,
}

/// 监控结果
#[derive(Clone, Debug)]
pub struct MonitoringResult {
    pub should_continue: bool,
    pub warnings: Vec<String>,
    pub recommended_action: RecommendedAction,
}

/// 推荐操作
#[derive(Clone, Debug, PartialEq)]
pub enum RecommendedAction {
    Proceed,
    Proceed_With_Caution,
    Abort,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::constants::{Route, RoutingMode};

    fn create_test_config(amount: u64, route_count: usize) -> RouteConfig {
        RouteConfig {
            mode: RoutingMode::Circular,
            routes: (0..route_count).map(|_| Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![],
            }).collect(),
            amount_in: amount,
            min_amount_out: amount * 99 / 100, // 1% 滑点
            max_slippage_bps: 100,
            flash_loan: None,
        }
    }

    fn create_test_market_conditions() -> MarketConditions {
        MarketConditions {
            raydium_liquidity: 1_000_000_000,
            orca_liquidity: 800_000_000,
            meteora_liquidity: 600_000_000,
            pumpswap_liquidity: 200_000_000,
            volatility_index: 500, // 5%
        }
    }

    #[test]
    fn test_assess_amount_risk() {
        let small_risk = ArbitrageRiskManager::assess_amount_risk(5_000_000);
        assert_eq!(small_risk.score, 5);

        let large_risk = ArbitrageRiskManager::assess_amount_risk(500_000_000);
        assert_eq!(large_risk.score, 18);

        let huge_risk = ArbitrageRiskManager::assess_amount_risk(2_000_000_000);
        assert_eq!(huge_risk.score, 25);
    }

    #[test]
    fn test_assess_liquidity_risk() {
        let config = create_test_config(10_000_000, 2);
        let market_conditions = create_test_market_conditions();

        let risk = ArbitrageRiskManager::assess_liquidity_risk(&config, &market_conditions);
        
        // 应该是低风险，因为流动性充足
        assert!(risk.score < 10);
    }

    #[test]
    fn test_full_risk_assessment() {
        let config = create_test_config(50_000_000, 3);
        let market_conditions = create_test_market_conditions();
        let expected_profit = 500_000; // 1% 利润

        let assessment = ArbitrageRiskManager::assess_arbitrage_risk(
            &config,
            expected_profit,
            &market_conditions,
        ).unwrap();

        // 应该是中等或低风险
        assert!(matches!(assessment.risk_level, RiskLevel::Low | RiskLevel::Medium));
        assert!(assessment.approved);
        assert!(!assessment.recommendations.is_empty());
    }

    #[test]
    fn test_monitor_arbitrage_execution() {
        let assessment = RiskAssessment {
            risk_level: RiskLevel::Medium,
            total_score: 35,
            risk_factors: vec![],
            recommendations: vec![],
            approved: true,
            max_recommended_amount: 100_000_000,
        };

        let intermediate_results = vec![1000000, 900000]; // 输出在下降
        
        let monitoring = ArbitrageRiskManager::monitor_arbitrage_execution(
            &assessment,
            2,
            4,
            &intermediate_results,
        ).unwrap();

        assert!(monitoring.should_continue);
        assert!(!monitoring.warnings.is_empty());
    }

    #[test]
    fn test_market_conditions() {
        let conditions = create_test_market_conditions();
        
        assert_eq!(conditions.get_dex_liquidity(&Dex::RaydiumClmm), 1_000_000_000);
        assert_eq!(conditions.get_dex_liquidity(&Dex::Orca), 800_000_000);
        
        let impact = conditions.estimate_price_impact(&Dex::RaydiumClmm, 10_000_000);
        assert!(impact < 100); // 应该小于1%的价格影响
    }
}