use anchor_lang::prelude::*;
use crate::{Dex, Route, RouteConfig, RouteError};

/// 套利利润计算器
pub struct ArbitrageProfitCalculator;


impl ArbitrageProfitCalculator {
    /// 创建新的利润计算器
    pub fn new() -> Self {
        Self
    }

    /// 计算预期套利利润
    pub fn calculate_profit(
        &self,
        config: &RouteConfig,
        flash_loan_amount: u64,
    ) -> anchor_lang::Result<u64> {
        // 验证是循环路由
        let start_token = config.routes.first().unwrap().input_mint;
        let end_token = config.routes.last().unwrap().output_mint;
        require!(
            start_token == end_token,
            RouteError::NotCircularRoute
        );

        // 模拟每步交易的输出
        let mut current_amount = flash_loan_amount;

        for route in &config.routes {
            // 估算每步的输出金额（简化计算）
            current_amount = self.estimate_step_output(current_amount, route)?;
        }

        // 计算利润（最终金额 - 初始借款金额）
        if current_amount > flash_loan_amount {
            Ok(current_amount - flash_loan_amount)
        } else {
            Ok(0) // 无利润或亏损
        }
    }


    /// 估算单步交易输出
    fn estimate_step_output(
        &self,
        amount_in: u64,
        route: &Route,
    ) -> anchor_lang::Result<u64> {
        // 获取DEX手续费率
        let fee_bps = self.get_dex_fee_bps(&route.dex);

        // 计算扣除手续费后的金额
        let fee_amount = amount_in
            .checked_mul(fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::MathOverflow)?;

        let amount_after_fee = amount_in
            .checked_sub(fee_amount)
            .ok_or(RouteError::MathOverflow)?;

        // 这里可以集成实际的价格预言机或AMM数学公式
        // 目前使用简化模型：假设1:1兑换率（扣除手续费）
        Ok(amount_after_fee)
    }

    /// 获取DEX手续费率（基点）
    fn get_dex_fee_bps(&self, dex: &Dex) -> u16 {
        match dex {
            Dex::RaydiumClmm => 25,  // 0.25%
            Dex::RaydiumCpmm => 25,  // 0.25%
            Dex::MeteoraDlmm => 20,  // 0.20%
            Dex::MeteoraAmm => 30,   // 0.30%
            Dex::Orca => 30,         // 0.30%
            Dex::PumpSwap => 100,    // 1.00%
        }
    }

    /// 计算总手续费成本
    pub fn calculate_total_fees(&self, config: &RouteConfig) -> Result<u64> {
        let mut total_fee_bps = 0u16;

        for route in &config.routes {
            total_fee_bps += self.get_dex_fee_bps(&route.dex);
        }

        let total_fee = config.amount_in
            .checked_mul(total_fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::MathOverflow)?;

        Ok(total_fee)
    }

    /// 计算价格影响
    pub fn calculate_price_impact(
        &self,
        amount_in: u64,
        liquidity_depth: u64,
    ) -> Result<u16> {
        if liquidity_depth == 0 {
            return Ok(10000); // 100%价格影响（无流动性）
        }

        // 简化价格影响模型：影响 = (金额 / 流动性深度) * 100
        let impact_ratio = (amount_in as f64) / (liquidity_depth as f64);
        let price_impact_bps = (impact_ratio * 10000.0).min(10000.0) as u16;

        Ok(price_impact_bps)
    }

    /// 验证套利机会的盈利性
    pub fn validate_profitability(
        &self,
        config: &RouteConfig,
        flash_loan_amount: u64,
        flash_loan_fee_bps: u16,
        min_profit_threshold: u64,
    ) -> Result<ProfitabilityAnalysis> {
        // 计算预期利润
        let gross_profit = self.calculate_profit(config, flash_loan_amount)?;

        // 计算闪电贷费用
        let flash_loan_fee = flash_loan_amount
            .checked_mul(flash_loan_fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::MathOverflow)?;

        // 计算净利润
        let net_profit = if gross_profit > flash_loan_fee {
            gross_profit - flash_loan_fee
        } else {
            0
        };

        // 计算利润率
        let profit_margin_bps = if flash_loan_amount > 0 {
            (net_profit * 10000) / flash_loan_amount
        } else {
            0
        } as u16;

        let is_profitable = net_profit >= min_profit_threshold;

        Ok(ProfitabilityAnalysis {
            gross_profit,
            flash_loan_fee,
            net_profit,
            profit_margin_bps,
            is_profitable,
            estimated_gas_cost: 20_000, // 估算Gas成本
        })
    }
}


/// 盈利性分析结果
#[derive(Clone, Debug)]
pub struct ProfitabilityAnalysis {
    pub gross_profit: u64,
    pub flash_loan_fee: u64,
    pub net_profit: u64,
    pub profit_margin_bps: u16,
    pub is_profitable: bool,
    pub estimated_gas_cost: u64,
}
