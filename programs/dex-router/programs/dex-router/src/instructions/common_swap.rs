use anchor_lang::prelude::*;
use anchor_spl::associated_token::AssociatedToken;
use anchor_spl::token_interface::{Mint, TokenAccount, TokenInterface};
use crate::{log_swap_balance_before, log_swap_basic_info, log_swap_end, CommonSwapProcessor, RouteExecutor, SwapArgs};
use crate::error::RouteError;

pub fn common_swap<'info, T: CommonSwapProcessor<'info>>(
    swap_processor: &T,
    payer: &AccountInfo<'info>,
    owner: &AccountInfo<'info>,
    owner_seeds: Option<&[&[&[u8]]]>,
    source_token_account: &mut InterfaceAccount<'info, TokenAccount>,
    destination_token_account: &mut InterfaceAccount<'info, TokenAccount>,
    source_mint: &InterfaceAccount<'info, Mint>,
    destination_mint: &InterfaceAccount<'info, Mint>,
    sa_authority: &Option<UncheckedAccount<'info>>,
    source_token_sa: &mut Option<UncheckedAccount<'info>>,
    destination_token_sa: &mut Option<UncheckedAccount<'info>>,
    source_token_program: &Option<Interface<'info, TokenInterface>>,
    destination_token_program: &Option<Interface<'info, TokenInterface>>,
    associated_token_program: &Option<Program<'info, AssociatedToken>>,
    system_program: &Option<Program<'info, System>>,
    remaining_accounts: &'info [AccountInfo<'info>],
    args: SwapArgs,
    order_id: u64,
    fee_rate: Option<u32>,
    fee_direction: Option<bool>,
    fee_token_account: Option<&InterfaceAccount<'info, TokenAccount>>,
) -> Result<u64> {
    log_swap_basic_info(
        order_id,
        &source_mint.key(),
        &destination_mint.key(),
        &source_token_account.owner,
        &destination_token_account.owner,
    );

    let before_source_balance = source_token_account.amount;
    let before_destination_balance = destination_token_account.amount;
    let min_return = args.min_return;

    log_swap_balance_before(
        before_source_balance,
        before_destination_balance,
        args.amount_in,
        args.expect_amount_out,
        min_return,
    );

    // get swap accounts
    let (mut source_account, mut destination_account) = swap_processor.get_swap_accounts(
        payer,
        source_token_account,
        destination_token_account,
        source_mint,
        destination_mint,
        sa_authority,
        source_token_sa,
        destination_token_sa,
        source_token_program,
        destination_token_program,
        associated_token_program,
        system_program,
    )?;

    // before swap hook
    let real_amount_in = swap_processor.before_swap(
        owner,
        source_token_account,
        source_mint,
        source_token_sa,
        source_token_program,
        args.amount_in,
        owner_seeds,
        fee_rate,
        fee_direction,
        fee_token_account,
    )?;

    // Common swap
    let amount_out = execute_swap(
        &mut source_account,
        &mut destination_account,
        remaining_accounts,
        args,
        real_amount_in,
        order_id,
        source_token_sa.is_some(),
        owner_seeds,
        Some(payer),
    )?;

    // after swap hook
    swap_processor.after_swap(
        sa_authority,
        destination_token_account,
        destination_mint,
        destination_token_sa,
        destination_token_program,
        amount_out,
        None,
        fee_rate,
        fee_direction,
        fee_token_account,
    )?;


    // source token account has been closed in pumpfun buy
    let after_source_balance = if source_token_account.get_lamports() != 0 {
        source_token_account.reload()?;
        source_token_account.amount
    } else {
        0
    };
    let source_token_change = before_source_balance
        .checked_sub(after_source_balance)
        .ok_or(RouteError::PriceCalculationError)?;

    destination_token_account.reload()?;
    let after_destination_balance = destination_token_account.amount;
    let destination_token_change = after_destination_balance
        .checked_sub(before_destination_balance)
        .ok_or(RouteError::PriceCalculationError)?;

    log_swap_end(
        after_source_balance,
        after_destination_balance,
        source_token_change,
        destination_token_change,
    );

    // Check min return
    require!(
        destination_token_change >= min_return,
        RouteError::MinReturnNotReached
    );
    Ok(destination_token_change)
}


fn execute_swap<'info>(
    source_account: &mut InterfaceAccount<'info, TokenAccount>,
    destination_account: &mut InterfaceAccount<'info, TokenAccount>,
    remaining_accounts: &'info [AccountInfo<'info>],
    args: SwapArgs,
    real_amount_in: u64,
    order_id: u64,
    proxy_from: bool,
    owner_seeds: Option<&[&[&[u8]]]>,
    payer: Option<&AccountInfo<'info>>,
) -> Result<u64> {
    destination_account.reload()?;
    let before_destination_balance = destination_account.amount;

    require!(real_amount_in > 0, RouteError::ZeroAmount);

    let fork_amount_out = RouteExecutor::execute_route(&args.route_config, remaining_accounts)?;

    destination_account.reload()?;
    let after_destination_balance = destination_account.amount;
    let amount_out = after_destination_balance
        .checked_sub(before_destination_balance)
        .ok_or(RouteError::PriceCalculationError)?;

    Ok(amount_out)
}
