use anchor_lang::prelude::*;
use crate::{meteora, orca, pumpswap, raydium, Dex};


pub fn distribute_swap<'a>(
    dex: &<PERSON>,
    remaining_accounts: &'a [AccountInfo<'a>],
    amount_in: u64,
    offset: &mut usize,
    from_account: Pubkey,
    to_account: Pubkey,
) -> Result<u64> {
    let swap_function = match dex {
        Dex::RaydiumClmm => raydium::swap_clmm,
        Dex::RaydiumCpmm => raydium::swap_cpmm,
        Dex::MeteoraDlmm => meteora::swap_dlmm,
        Dex::MeteoraAmm => meteora::swap_dlmm,
        Dex::PumpSwap => pumpswap::buy,
        Dex::Orca => orca::swap,
    };
    swap_function(remaining_accounts, amount_in, offset, from_account, to_account)
}
