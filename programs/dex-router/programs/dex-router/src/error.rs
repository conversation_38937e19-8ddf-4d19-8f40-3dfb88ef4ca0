use anchor_lang::prelude::*;

/// DEX路由器错误类型
/// 遵循分类和层次化的错误设计，便于调试和错误处理
#[error_code]
pub enum RouteError {
    // === 路由配置相关错误 (6000-6099) ===
    #[msg("无效的路由配置")]
    InvalidRouteConfig = 6000,

    #[msg("路由路径为空")]
    EmptyRoutePath = 6001,

    #[msg("路由路径过长，超过最大允许步骤数")]
    RoutePathTooLong = 6002,

    #[msg("路由路径不连续，相邻步骤的代币不匹配")]
    RouteDiscontinuity = 6003,

    #[msg("循环路由验证失败，起始和结束代币不匹配")]
    NotCircularRoute = 6004,

    #[msg("无效的路由模式")]
    InvalidRoutingMode = 6005,

    #[msg("分支路由配置无效")]
    InvalidBranchConfig = 6006,

    #[msg("批量路由配置无效")]
    InvalidBatchConfig = 6007,

    #[msg("路由步骤数无效")]
    InvalidRouteSteps = 6008,

    #[msg("路由为空")]
    EmptyRoute = 6009,

    #[msg("路由步骤过多")]
    TooManyRouteSteps = 6010,

    #[msg("零金额")]
    ZeroAmount = 6011,

    #[msg("输出金额不足")]
    InsufficientOutput = 6012,

    #[msg("无效的循环路由")]
    InvalidCircularRoute = 6013,

    #[msg("分支过多")]
    TooManyBranches = 6014,

    #[msg("无效的分配比例")]
    InvalidAllocation = 6015,

    #[msg("批量路由过多")]
    TooManyBatchRoutes = 6016,

    #[msg("不支持的批量路由模式")]
    UnsupportedBatchRouteMode = 6017,

    #[msg("不支持的路由模式")]
    UnsupportedRouteMode = 6018,

    #[msg("数据不足")]
    InsufficientData = 6019,

    #[msg("找不到最优路由")]
    NoOptimalRoute = 6020,

    #[msg("无效的批量大小")]
    InvalidBatchSize = 6021,

    #[msg("批量执行失败")]
    BatchExecutionFailed = 6022,

    #[msg("操作截止时间已过")]
    DeadlineExceeded = 6023,

    #[msg("无效的滑点设置")]
    InvalidSlippage = 6024,

    // === DEX操作相关错误 (6100-6199) ===
    #[msg("不支持的DEX协议")]
    UnsupportedDex = 6100,

    #[msg("DEX适配器执行失败")]
    DexAdapterFailed = 6101,

    #[msg("流动性不足")]
    InsufficientLiquidity = 6102,

    #[msg("滑点超过允许范围")]
    SlippageTooHigh = 6103,

    #[msg("价格影响超过允许范围")]
    PriceImpactTooHigh = 6104,

    #[msg("DEX账户验证失败")]
    InvalidDexAccounts = 6105,

    #[msg("DEX程序调用失败")]
    DexCpiCallFailed = 6106,

    #[msg("DEX池配置无效")]
    InvalidPoolConfig = 6107,

    #[msg("代币mint不匹配")]
    TokenMintMismatch = 6108,

    // === 安全和权限相关错误 (6200-6299) ===
    #[msg("全局紧急停止已启动")]
    GlobalEmergencyStop = 6200,

    #[msg("DEX紧急停止已启动")]
    DexEmergencyStop = 6201,

    #[msg("重入攻击检测")]
    ReentrancyDetected = 6202,

    #[msg("用户被暂停")]
    UserSuspended = 6203,

    #[msg("权限验证失败")]
    PermissionDenied = 6204,

    #[msg("签名验证失败")]
    InvalidSignature = 6205,

    #[msg("管理员权限验证失败")]
    AdminPermissionDenied = 6206,

    #[msg("操作频率超限")]
    RateLimitExceeded = 6207,

    // === 闪电贷相关错误 (6300-6399) ===
    #[msg("闪电贷提供者不可用")]
    FlashLoanProviderUnavailable = 6300,

    #[msg("闪电贷金额超过限制")]
    FlashLoanAmountExceeded = 6301,

    #[msg("闪电贷还款失败")]
    FlashLoanRepaymentFailed = 6302,

    #[msg("闪电贷费用计算错误")]
    FlashLoanFeeCalculationError = 6303,

    #[msg("利润计算错误")]
    ProfitCalculationError = 6304,

    #[msg("不支持的闪电贷提供商")]
    UnsupportedFlashLoanProvider = 6305,


    #[msg("闪电贷费用过高")]
    ExcessiveFlashLoanFee = 6307,

    #[msg("没有可用的闪电贷提供商")]
    NoFlashLoanProvider = 6308,

    #[msg("账户数量不足")]
    InsufficientAccounts = 6309,

    #[msg("无效的程序ID")]
    InvalidProgram = 6310,

    #[msg("无效的回调数据")]
    InvalidCallbackData = 6311,

    #[msg("不支持的回调类型")]
    UnsupportedCallbackType = 6312,

    // === 套利相关错误 (6400-6499) ===
    #[msg("套利风险过高")]
    ArbitrageRiskTooHigh = 6400,

    #[msg("无效的三角套利路由")]
    InvalidTriangularRoute = 6401,

    #[msg("无效的跨DEX套利路由")]
    InvalidCrossDexRoute = 6402,

    #[msg("没有最优路由解决方案")]
    NoOptimalRouteSolution = 6403,

    #[msg("套利利润不足")]
    InsufficientArbitrageProfit = 6404,

    #[msg("闪电贷回调执行失败")]
    FlashLoanCallbackFailed = 6405,

    #[msg("闪电贷提供者不在白名单")]
    FlashLoanProviderNotWhitelisted = 6406,

    #[msg("无效的闪电贷账户")]
    InvalidFlashLoanAccounts = 6407,

    #[msg("闪电贷偿还不足")]
    InsufficientFlashLoanRepay = 6408,

    #[msg("无效的闪电贷配置")]
    InvalidFlashLoanConfig = 6409,

    // === 账户验证错误 (6500-6599) ===
    #[msg("无效的账户")]
    InvalidAccount = 6500,

    #[msg("账户余额不足")]
    InsufficientBalance = 6501,

    #[msg("代币账户所有者不匹配")]
    TokenAccountOwnerMismatch = 6502,

    #[msg("PDA验证失败")]
    InvalidPda = 6503,

    #[msg("账户数据不完整")]
    IncompleteAccountData = 6504,

    #[msg("账户已初始化")]
    AccountAlreadyInitialized = 6505,

    #[msg("账户未初始化")]
    AccountNotInitialized = 6506,

    #[msg("交换权限无效")]
    InvalidSwapAuthority = 6507,

    #[msg("交换权限不是签名者")]
    SwapAuthorityIsNotSigner = 6508,

    // === 数学计算错误 (6600-6699) ===
    #[msg("数学运算溢出")]
    MathOverflow = 6600,

    #[msg("数学运筗下溢")]
    MathUnderflow = 6601,

    #[msg("除零错误")]
    DivisionByZero = 6602,

    #[msg("无效的计算参数")]
    InvalidCalculation = 6603,

    #[msg("精度损失")]
    PrecisionLoss = 6604,

    #[msg("价格计算错误")]
    PriceCalculationError = 6605,

    #[msg("滑点计算错误")]
    SlippageCalculationError = 6606,

    // === 系统错误 (6700-6799) ===
    #[msg("系统配置错误")]
    SystemConfigError = 6700,

    #[msg("时间戳错误")]
    TimestampError = 6701,

    #[msg("网络错误")]
    NetworkError = 6702,

    #[msg("资源不足")]
    InsufficientResources = 6703,

    #[msg("操作超时")]
    OperationTimeout = 6704,

    #[msg("未知错误")]
    UnknownError = 6799,

    // === 指令约束错误 (6800-6899) ===
    #[msg("用户未授权")]
    UnauthorizedUser = 6800,

    #[msg("代币账户所有者无效")]
    InvalidTokenAccountOwner = 6801,

    #[msg("输出金额不足")]
    InsufficientOutputAmount = 6802,

    #[msg("无效的套利模式")]
    InvalidArbitrageMode = 6803,

    #[msg("闪电贷功能被禁用")]
    FlashLoanDisabled = 6804,

    #[msg("闪电贷未获批")]
    FlashLoanNotApproved = 6805,

    #[msg("无效的PDA")]
    InvalidPDA = 6806,

    #[msg("数据过旧")]
    DataTooOld = 6807,

    #[msg("未来时间戳")]
    FutureTimestamp = 6808,

    #[msg("不盈利的套利")]
    UnprofitableArbitrage = 6809,

    #[msg("利润太低")]
    ProfitTooLow = 6810,

    #[msg("管理员未授权")]
    UnauthorizedAdmin = 6811,

    #[msg("紧急联系人过多")]
    TooManyEmergencyContacts = 6812,

    #[msg("无效的冷却期")]
    InvalidCooldownPeriod = 6813,

    #[msg("无效的失败率阈值")]
    InvalidFailureRateThreshold = 6814,

    #[msg("无效的监控窗口")]
    InvalidMonitoringWindow = 6815,


    #[msg("未授权的闪电贷提供者")]
    UnauthorizedFlashLoanProvider = 6817,

    #[msg("日限额超出")]
    DailyLimitExceeded = 6818,

    #[msg("风险太高")]
    RiskTooHigh = 6819,

    #[msg("Min return not reached")]
    MinReturnNotReached = 6836,

    #[msg("Invalid accounts length")]
    InvalidAccountsLength = 6837,

    // === 余额计算相关错误 ===
    #[msg("计算错误")]
    CalculationError = 6820,

    #[msg("Invalid actual amount in")]
    InvalidActualAmountIn,

    #[msg("Amount out must be greater than 0")]
    AmountOutMustBeGreaterThanZero,

    #[msg("账户余额重新加载失败")]
    AccountReloadFailed = 6830,

    #[msg("实际输入金额超出预期")]
    ExcessiveAmountIn = 6831,

    #[msg("实际输出金额为零")]
    ZeroAmountOut = 6832,

    #[msg("未授权的代币账户")]
    UnauthorizedTokenAccount = 6833,
}

/// 错误恢复策略
#[derive(Debug, Clone, PartialEq)]
pub enum FallbackAction {
    /// 重试操作
    Retry,
    /// 使用备用路径
    UseFallbackRoute,
    /// 停止执行
    Stop,
    /// 降级执行（减少复杂度）
    Degrade,
}

/// 重试策略配置
#[derive(Debug, Clone)]
pub struct RetryStrategy {
    pub max_attempts: u8,
    pub delay_ms: u64,
    pub exponential_backoff: bool,
}

impl Default for RetryStrategy {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            delay_ms: 100,
            exponential_backoff: true,
        }
    }
}

/// 错误恢复特征
pub trait ErrorRecovery {
    /// 确定错误的恢复策略
    fn get_fallback_action(&self) -> FallbackAction;

    /// 检查是否可以重试
    fn is_retryable(&self) -> bool;

    /// 获取重试策略
    fn get_retry_strategy(&self) -> RetryStrategy;
}

impl ErrorRecovery for RouteError {
    fn get_fallback_action(&self) -> FallbackAction {
        match self {
            // 路由相关错误可以尝试备用路径
            RouteError::RouteDiscontinuity |
            RouteError::InsufficientLiquidity |
            RouteError::SlippageTooHigh => FallbackAction::UseFallbackRoute,

            // 权限和安全错误应该停止
            RouteError::GlobalEmergencyStop |
            RouteError::UserSuspended |
            RouteError::PermissionDenied => FallbackAction::Stop,

            // 临时性错误可以重试
            RouteError::DexCpiCallFailed |
            RouteError::NetworkError |
            RouteError::OperationTimeout => FallbackAction::Retry,

            // 其他错误降级处理
            _ => FallbackAction::Degrade,
        }
    }

    fn is_retryable(&self) -> bool {
        matches!(self.get_fallback_action(), FallbackAction::Retry)
    }

    fn get_retry_strategy(&self) -> RetryStrategy {
        match self {
            RouteError::DexCpiCallFailed => RetryStrategy {
                max_attempts: 3,
                delay_ms: 200,
                exponential_backoff: true,
            },
            RouteError::NetworkError => RetryStrategy {
                max_attempts: 5,
                delay_ms: 500,
                exponential_backoff: true,
            },
            _ => RetryStrategy::default(),
        }
    }
}
