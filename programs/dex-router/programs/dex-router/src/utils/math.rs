use anchor_lang::prelude::*;
use crate::<PERSON>;

/// 数学计算工具
pub fn safe_add(a: u64, b: u64) -> Result<u64> {
    a.checked_add(b).ok_or_else(|| crate::error::RouteError::MathOverflow.into())
}

pub fn safe_sub(a: u64, b: u64) -> Result<u64> {
    a.checked_sub(b).ok_or_else(|| crate::error::RouteError::MathOverflow.into())
}

pub fn safe_mul(a: u64, b: u64) -> Result<u64> {
    a.checked_mul(b).ok_or_else(|| crate::error::RouteError::MathOverflow.into())
}

pub fn safe_div(a: u64, b: u64) -> Result<u64> {
    a.checked_div(b).ok_or_else(|| crate::error::RouteError::MathOverflow.into())
}

/// 计算百分比
pub fn calculate_percentage(amount: u64, percentage_bps: u16) -> Result<u64> {
    safe_mul(amount, percentage_bps as u64)
        .and_then(|result| safe_div(result, 10000))
}

/// 获取DEX需要的账户数量（占位函数）
pub fn get_accounts_needed_for_dex(dex: &Dex) -> u8 {
    match dex {
        Dex::RaydiumClmm => 10,
        Dex::RaydiumCpmm => 8,
        Dex::MeteoraDlmm => 12,
        Dex::MeteoraAmm => 8,
        Dex::Orca => 10,
        Dex::PumpSwap => 6,
    }
}
