use anchor_lang::prelude::*;
use crate::RouteError;

/// PDA种子常量
pub mod seeds {
    /// 配置账户种子
    pub const CONFIG: &[u8] = b"config";

    /// 用户位置账户种子
    pub const POSITION: &[u8] = b"position";

    /// 闪电贷账户种子
    pub const FLASH_LOAN: &[u8] = b"flash_loan";

    /// 临时交换账户种子
    pub const TEMP_SWAP: &[u8] = b"temp_swap";
}

/// 路由限制常量
pub mod route_limits {
    /// 最大路由步骤数
    pub const MAX_ROUTE_STEPS: usize = 6;

    /// 最小路由步骤数
    pub const MIN_ROUTE_STEPS: usize = 1;

    /// 最大分支路由数量
    pub const MAX_BRANCH_ROUTES: usize = 4;

    /// 最大批量路由数量
    pub const MAX_BATCH_ROUTES: usize = 8;
}

/// 金额限制常量
pub mod amount_limits {
    /// 最小路由金额（1 USDC，6位小数）
    pub const MIN_ROUTE_AMOUNT: u64 = 1_000_000;

    /// 默认最大路由金额（1M USDC）
    pub const DEFAULT_MAX_ROUTE_AMOUNT: u64 = 1_000_000_000_000;

    /// 默认最大闪电贷金额（10M USDC）
    pub const DEFAULT_MAX_FLASH_LOAN_AMOUNT: u64 = 10_000_000_000_000;

    /// 最小利润阈值（1 USDC）
    pub const MIN_PROFIT_THRESHOLD: u64 = 1_000_000;
}

/// 费率常量（基点）
pub mod fee_rates {
    /// 最大滑点保护（10%）
    pub const MAX_SLIPPAGE_BPS: u16 = 1000;

    /// 默认最大滑点（3%）
    pub const DEFAULT_MAX_SLIPPAGE_BPS: u16 = 300;

    /// 默认协议费率（0.3%）
    pub const DEFAULT_PROTOCOL_FEE_BPS: u16 = 30;

    /// 最大协议费率（1%）
    pub const MAX_PROTOCOL_FEE_BPS: u16 = 100;

    /// 基点精度
    pub const BPS_PRECISION: u16 = 10000;
}

/// 时间常量
pub mod time_constants {
    /// 一天的秒数
    pub const DAY_IN_SECONDS: i64 = 24 * 60 * 60;

    /// 一小时的秒数
    pub const HOUR_IN_SECONDS: i64 = 60 * 60;

    /// 默认操作超时时间（30秒）
    pub const DEFAULT_TIMEOUT_SECONDS: i64 = 30;

    /// 闪电贷最大持续时间（1分钟）
    pub const FLASH_LOAN_MAX_DURATION: i64 = 60;
}

/// 风险管理常量
pub mod risk_management {
    /// 最大风险评分
    pub const MAX_RISK_SCORE: u8 = 10;

    /// 默认风险评分
    pub const DEFAULT_RISK_SCORE: u8 = 5;

    /// 最大用户等级
    pub const MAX_USER_LEVEL: u8 = 5;

    /// 风险阈值 - 高风险
    pub const HIGH_RISK_THRESHOLD: u8 = 7;

    /// 风险阈值 - 临界风险
    pub const CRITICAL_RISK_THRESHOLD: u8 = 9;
}

/// DEX标识常量
pub mod dex_ids {
    pub const RAYDIUM_CLMM: u8 = 0;
    pub const RAYDIUM_CPMM: u8 = 1;
    pub const METEORA_DLMM: u8 = 2;
    pub const METEORA_AMM: u8 = 3;
    pub const ORCA_WHIRLPOOL: u8 = 4;
    pub const PUMPSWAP: u8 = 5;
}

/// 路由模式常量
pub mod route_modes {
    pub const LINEAR: u8 = 0;
    pub const CIRCULAR: u8 = 1;
    pub const BRANCHING: u8 = 2;
    pub const BATCHED: u8 = 3;
}

/// 数学常量
pub mod math_constants {
    /// 精度因子（用于高精度计算）
    pub const PRECISION_FACTOR: u64 = 1_000_000;

    /// 最大u64值（用于溢出检查）
    pub const MAX_U64: u64 = u64::MAX;

    /// 最小非零值
    pub const MIN_NON_ZERO: u64 = 1;
}

/// DEX枚举定义
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub enum Dex {
    RaydiumClmm = 0,
    RaydiumCpmm = 1,
    MeteoraDlmm = 2,
    MeteoraAmm = 3,
    Orca = 4,
    PumpSwap = 5,
}

impl From<u8> for Dex {
    fn from(value: u8) -> Self {
        match value {
            0 => Dex::RaydiumClmm,
            1 => Dex::RaydiumCpmm,
            2 => Dex::MeteoraDlmm,
            3 => Dex::MeteoraAmm,
            4 => Dex::Orca,
            5 => Dex::PumpSwap,
            _ => Dex::RaydiumClmm,
        }
    }
}

impl From<Dex> for u8 {
    fn from(dex: Dex) -> Self {
        match dex {
            Dex::RaydiumClmm => 0,
            Dex::RaydiumCpmm => 1,
            Dex::MeteoraDlmm => 2,
            Dex::MeteoraAmm => 3,
            Dex::Orca => 4,
            Dex::PumpSwap => 5,
        }
    }
}

/// 路由模式枚举定义
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug, PartialEq)]
pub enum RoutingMode {
    /// 线性路由：A -> B -> C
    Linear = 0,
    /// 循环路由：A -> B -> C -> A（套利模式）
    Circular = 1,
    /// 分支路由：A -> [B, C] -> D（分散-聚合）
    Branching = 2,
    /// 批量路由：[A1, A2] -> [B1, B2]（并行处理）
    Batched = 3,
}

impl From<u8> for RoutingMode {
    fn from(value: u8) -> Self {
        match value {
            0 => RoutingMode::Linear,
            1 => RoutingMode::Circular,
            2 => RoutingMode::Branching,
            3 => RoutingMode::Batched,
            _ => RoutingMode::Linear, // 默认值
        }
    }
}

impl From<RoutingMode> for u8 {
    fn from(mode: RoutingMode) -> Self {
        match mode {
            RoutingMode::Linear => 0,
            RoutingMode::Circular => 1,
            RoutingMode::Branching => 2,
            RoutingMode::Batched => 3,
        }
    }
}

/// 单个路由定义
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct Route {
    /// DEX类型
    pub dex: Dex,
    /// 输入代币mint
    pub input_mint: Pubkey,
    /// 输出代币mint
    pub output_mint: Pubkey,
}

/// 路由配置定义
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RouteConfig {
    /// 路由模式
    pub mode: RoutingMode,
    /// 路由步骤列表
    pub routes: Vec<Route>,
    /// 输入金额
    pub amount_in: u64,
    /// 最小输出金额
    pub min_amount_out: u64,
    /// 最大滑点（基点）
    pub max_slippage_bps: u16,
    /// 闪电贷配置（可选）
    pub flash_loan: Option<FlashLoanConfig>,
}

/// 闪电贷配置定义
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct FlashLoanConfig {
    /// 闪电贷提供者类型
    pub provider: u8, // 使用 u8 而不是 enum 以避免导入问题
    /// 闪电贷提供者程序ID
    pub provider_program: Pubkey,
    /// 借贷者账户
    pub borrower: Pubkey,
    /// 借贷金额
    pub amount: u64,
    /// 最大费用率（基点）
    pub max_fee_bps: u16,
}

/// 分支路由配置
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct BranchRouteConfig {
    /// 输入代币mint
    pub input_mint: Pubkey,
    /// 输出代币mint
    pub output_mint: Pubkey,
    /// 分支路由列表
    pub branches: Vec<RouteConfig>,
    /// 输入分配比例（基点）
    pub allocation_bps: Vec<u16>,
}

/// 批量路由配置
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct BatchRouteConfig {
    /// 路由列表
    pub routes: Vec<RouteConfig>,
    /// 是否原子性执行（全部成功或全部失败）
    pub atomic: bool,
}

// Actual amount_in lower bound ratio for post swap check
pub const ACTUAL_IN_LOWER_BOUND_NUM: u128 = 90; // 90%
pub const ACTUAL_IN_LOWER_BOUND_DEN: u128 = 100; // denominator for percentage



pub const ZERO_ADDRESS: Pubkey = Pubkey::new_from_array([0u8; 32]);
pub const SWAP_V2_SELECTOR: &[u8; 8] = &[43, 4, 237, 11, 26, 201, 30, 98];
pub const CPSWAP_SELECTOR: &[u8; 8] = &[143, 190, 90, 218, 196, 30, 51, 222];



pub mod raydium_swap_program {
    use anchor_lang::declare_id;
    declare_id!("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8");
}

pub mod raydium_stable_program {
    use anchor_lang::declare_id;
    declare_id!("5quBtoiQqxF9Jv6KYKctB59NT3gtJD2Y65kdnB1Uev3h");
}

pub mod raydium_clmm_program {
    use anchor_lang::declare_id;
    declare_id!("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK");
}

pub mod raydium_cpmm_program {
    use anchor_lang::declare_id;
    declare_id!("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C");
}

/// 程序版本常量
pub const PROGRAM_VERSION: &str = "1.0.0";

/// 程序名称
pub const PROGRAM_NAME: &str = "DEX Router";
