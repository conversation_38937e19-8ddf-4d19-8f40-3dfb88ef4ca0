//! Kamino 闪电贷适配器
//!
//! 与 Kamino Protocol 的闪电贷功能集成

use anchor_lang::prelude::*;
use anchor_lang::solana_program::instruction::Instruction;
use crate::constants::FlashLoanConfig;
use crate::error::RouteError;
use crate::flash_loan::{FlashLoanAvailability, FlashLoanProvider};

/// Kamino 闪电贷适配器
pub struct KaminoFlashLoanAdapter;

impl KaminoFlashLoanAdapter {
    /// 执行 Kamino 闪电贷
    pub fn execute_flash_loan<'info>(
        config: &FlashLoanConfig,
        accounts: &'info [AccountInfo<'info>],
        callback_data: &[u8],
    ) -> Result<u64> {
        // 验证账户数量
        require!(
            accounts.len() >= Self::get_required_accounts_count(),
            RouteError::InsufficientAccounts
        );

        // 解析账户
        let kamino_accounts = Self::parse_accounts(accounts)?;

        // 验证 Kamino 程序 ID
        require!(
            *kamino_accounts.kamino_program.key == Self::get_kamino_program_id(),
            RouteError::InvalidProgram
        );

        // 构建闪电贷指令
        let flash_loan_ix = Self::build_flash_loan_instruction(
            config,
            &kamino_accounts,
            callback_data,
        )?;

        // 执行指令 (在实际实现中，这里会进行 CPI 调用)
        msg!("执行 Kamino 闪电贷: 金额 {}", config.amount);

        // 模拟成功执行并返回利润
        // 在实际实现中，这里会从回调中获取实际结果
        let simulated_profit = Self::simulate_execution(config, callback_data)?;

        Ok(simulated_profit)
    }

    /// 检查 Kamino 闪电贷可用性
    pub fn check_availability(amount: u64) -> Result<FlashLoanAvailability> {
        // 在实际实现中，这里会查询 Kamino 的池子状态
        let mock_pool_liquidity = 1_000_000_000_000u64; // 1M SOL
        let mock_current_borrows = 300_000_000_000u64;   // 300K SOL

        let available_liquidity = mock_pool_liquidity.saturating_sub(mock_current_borrows);
        let utilization_rate = if mock_pool_liquidity > 0 {
            (mock_current_borrows * 10000 / mock_pool_liquidity) as u16
        } else {
            10000
        };

        Ok(FlashLoanAvailability {
            available: amount <= available_liquidity,
            max_amount: available_liquidity,
            current_utilization_bps: utilization_rate,
            estimated_fee_bps: Self::get_fee_rate(utilization_rate),
        })
    }

    /// 获取 Kamino 程序 ID
    pub fn get_kamino_program_id() -> Pubkey {
        // Kamino Protocol 主网程序 ID（硬编码）
        Pubkey::new_unique() // 在实际实现中应该使用真实的程序ID
    }

    /// 获取所需账户数量
    pub fn get_required_accounts_count() -> usize {
        8 // Kamino 闪电贷需要的基本账户数量
    }

    /// 解析账户
    fn parse_accounts<'info>(accounts: &'info [AccountInfo<'info>]) -> Result<KaminoAccounts<'info>> {
        require!(
            accounts.len() >= 8,
            RouteError::InsufficientAccounts
        );

        Ok(KaminoAccounts {
            kamino_program: &accounts[0],
            lending_market: &accounts[1],
            reserve: &accounts[2],
            reserve_liquidity_supply: &accounts[3],
            reserve_collateral_mint: &accounts[4],
            lending_market_authority: &accounts[5],
            borrower: &accounts[6],
            token_program: &accounts[7],
        })
    }

    /// 构建闪电贷指令
    fn build_flash_loan_instruction(
        config: &FlashLoanConfig,
        accounts: &KaminoAccounts,
        callback_data: &[u8],
    ) -> Result<Instruction> {
        // 构建 Kamino 闪电贷指令数据
        let mut instruction_data = vec![0u8]; // Kamino 闪电贷指令标识符
        instruction_data.extend_from_slice(&config.amount.to_le_bytes());
        instruction_data.extend_from_slice(callback_data);

        let instruction = Instruction {
            program_id: Self::get_kamino_program_id(),
            accounts: vec![
                AccountMeta::new(*accounts.lending_market.key, false),
                AccountMeta::new(*accounts.reserve.key, false),
                AccountMeta::new(*accounts.reserve_liquidity_supply.key, false),
                AccountMeta::new_readonly(*accounts.reserve_collateral_mint.key, false),
                AccountMeta::new_readonly(*accounts.lending_market_authority.key, false),
                AccountMeta::new(*accounts.borrower.key, true),
                AccountMeta::new_readonly(*accounts.token_program.key, false),
            ],
            data: instruction_data,
        };

        Ok(instruction)
    }

    /// 模拟执行（用于测试）
    fn simulate_execution(config: &FlashLoanConfig, callback_data: &[u8]) -> Result<u64> {
        // 模拟套利逻辑执行
        if callback_data.is_empty() {
            return Ok(0);
        }

        // 假设套利能够产生 0.1% 的利润
        let gross_profit = config.amount / 1000;
        
        // 减去闪电贷费用 (0.05% = 5 bps)
        let flash_loan_fee = config.amount * 5 / 10000;
        
        let net_profit = gross_profit.saturating_sub(flash_loan_fee);
        
        msg!("模拟套利执行: 毛利润 {}, 闪电贷费用 {}, 净利润 {}", 
             gross_profit, flash_loan_fee, net_profit);

        Ok(net_profit)
    }

    /// 获取基于利用率的费率
    fn get_fee_rate(utilization_bps: u16) -> u16 {
        match utilization_bps {
            0..=5000 => 5,      // 0-50% 利用率: 0.05%
            5001..=7500 => 8,   // 50-75% 利用率: 0.08%
            7501..=9000 => 12,  // 75-90% 利用率: 0.12%
            9001..=9500 => 20,  // 90-95% 利用率: 0.20%
            _ => 50,            // >95% 利用率: 0.50%
        }
    }

    /// 估算最优借贷金额
    pub fn estimate_optimal_amount(
        available_liquidity: u64,
        target_profit_bps: u16,
    ) -> Result<u64> {
        // 基于可用流动性和目标利润率计算最优金额
        let max_safe_amount = available_liquidity / 2; // 不超过池子的50%
        
        // 基于目标利润率调整
        let adjusted_amount = if target_profit_bps > 100 { // >1% 目标
            max_safe_amount / 4 // 更保守
        } else if target_profit_bps > 50 { // >0.5% 目标
            max_safe_amount / 2
        } else {
            max_safe_amount
        };

        Ok(adjusted_amount)
    }

    /// 计算闪电贷回调所需的最小账户
    pub fn get_callback_accounts_count(callback_type: CallbackType) -> usize {
        match callback_type {
            CallbackType::SimpleArbitrage => 12,
            CallbackType::MultiDexArbitrage => 20,
            CallbackType::LiquidationArbitrage => 15,
        }
    }

    /// 验证闪电贷回调数据
    pub fn validate_callback_data(data: &[u8]) -> Result<CallbackInstruction> {
        require!(
            data.len() >= 1,
            RouteError::InvalidCallbackData
        );

        let instruction_type = data[0];
        
        match instruction_type {
            0 => {
                // 简单套利
                require!(
                    data.len() >= 33, // 1 + 32 (pubkey)
                    RouteError::InvalidCallbackData
                );
                
                let target_dex = Pubkey::new_from_array(
                    data[1..33].try_into().map_err(|_| RouteError::InvalidCallbackData)?
                );
                
                Ok(CallbackInstruction::SimpleArbitrage { target_dex })
            },
            1 => {
                // 多 DEX 套利
                require!(
                    data.len() >= 2,
                    RouteError::InvalidCallbackData
                );
                
                let dex_count = data[1] as usize;
                require!(
                    data.len() >= 2 + dex_count * 32,
                    RouteError::InvalidCallbackData
                );
                
                let mut dex_list = Vec::new();
                for i in 0..dex_count {
                    let start = 2 + i * 32;
                    let end = start + 32;
                    let dex_pubkey = Pubkey::new_from_array(
                        data[start..end].try_into().map_err(|_| RouteError::InvalidCallbackData)?
                    );
                    dex_list.push(dex_pubkey);
                }
                
                Ok(CallbackInstruction::MultiDexArbitrage { dex_list })
            },
            _ => Err(RouteError::UnsupportedCallbackType.into()),
        }
    }
}

/// Kamino 账户结构
pub struct KaminoAccounts<'info> {
    /// Kamino 程序
    pub kamino_program: &'info AccountInfo<'info>,
    /// 借贷市场
    pub lending_market: &'info AccountInfo<'info>,
    /// 储备账户
    pub reserve: &'info AccountInfo<'info>,
    /// 储备流动性供应账户
    pub reserve_liquidity_supply: &'info AccountInfo<'info>,
    /// 储备抵押代币铸造账户
    pub reserve_collateral_mint: &'info AccountInfo<'info>,
    /// 借贷市场权限
    pub lending_market_authority: &'info AccountInfo<'info>,
    /// 借贷者
    pub borrower: &'info AccountInfo<'info>,
    /// Token 程序
    pub token_program: &'info AccountInfo<'info>,
}

/// 回调指令类型
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum CallbackInstruction {
    SimpleArbitrage {
        target_dex: Pubkey,
    },
    MultiDexArbitrage {
        dex_list: Vec<Pubkey>,
    },
    LiquidationArbitrage {
        target_account: Pubkey,
        liquidation_amount: u64,
    },
}

/// 回调类型
#[derive(Clone, Copy, Debug)]
pub enum CallbackType {
    SimpleArbitrage,
    MultiDexArbitrage,
    LiquidationArbitrage,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_check_availability() {
        let availability = KaminoFlashLoanAdapter::check_availability(100_000_000).unwrap();
        
        assert!(availability.available);
        assert!(availability.max_amount > 100_000_000);
        assert!(availability.current_utilization_bps <= 10000);
    }

    #[test]
    fn test_get_fee_rate() {
        assert_eq!(KaminoFlashLoanAdapter::get_fee_rate(2500), 5);  // 25% 利用率
        assert_eq!(KaminoFlashLoanAdapter::get_fee_rate(6000), 8);  // 60% 利用率
        assert_eq!(KaminoFlashLoanAdapter::get_fee_rate(8000), 12); // 80% 利用率
        assert_eq!(KaminoFlashLoanAdapter::get_fee_rate(9200), 20); // 92% 利用率
        assert_eq!(KaminoFlashLoanAdapter::get_fee_rate(9800), 50); // 98% 利用率
    }

    #[test]
    fn test_estimate_optimal_amount() {
        let available = 1_000_000_000u64;
        
        let optimal_high = KaminoFlashLoanAdapter::estimate_optimal_amount(available, 150).unwrap();
        let optimal_medium = KaminoFlashLoanAdapter::estimate_optimal_amount(available, 75).unwrap();
        let optimal_low = KaminoFlashLoanAdapter::estimate_optimal_amount(available, 25).unwrap();
        
        assert!(optimal_high < optimal_medium);
        assert!(optimal_medium < optimal_low);
        assert!(optimal_low <= available / 2);
    }

    #[test]
    fn test_validate_callback_data() {
        // 测试简单套利回调数据
        let mut callback_data = vec![0u8]; // SimpleArbitrage
        let target_dex = Pubkey::new_unique();
        callback_data.extend_from_slice(&target_dex.to_bytes());
        
        let instruction = KaminoFlashLoanAdapter::validate_callback_data(&callback_data).unwrap();
        
        match instruction {
            CallbackInstruction::SimpleArbitrage { target_dex: parsed_dex } => {
                assert_eq!(parsed_dex, target_dex);
            },
            _ => panic!("Wrong instruction type parsed"),
        }

        // 测试多 DEX 套利回调数据
        let mut multi_dex_data = vec![1u8, 2u8]; // MultiDexArbitrage with 2 DEXes
        let dex1 = Pubkey::new_unique();
        let dex2 = Pubkey::new_unique();
        multi_dex_data.extend_from_slice(&dex1.to_bytes());
        multi_dex_data.extend_from_slice(&dex2.to_bytes());
        
        let multi_instruction = KaminoFlashLoanAdapter::validate_callback_data(&multi_dex_data).unwrap();
        
        match multi_instruction {
            CallbackInstruction::MultiDexArbitrage { dex_list } => {
                assert_eq!(dex_list.len(), 2);
                assert_eq!(dex_list[0], dex1);
                assert_eq!(dex_list[1], dex2);
            },
            _ => panic!("Wrong instruction type parsed"),
        }
    }

    #[test]
    fn test_simulate_execution() {
        let config = FlashLoanConfig {
            provider: 0, // Kamino
            provider_program: KaminoFlashLoanAdapter::get_kamino_program_id(),
            borrower: Pubkey::new_unique(),
            amount: 1_000_000_000, // 1000 tokens
            max_fee_bps: 100,
        };

        let callback_data = vec![1u8, 2u8, 3u8]; // Non-empty callback data
        
        let profit = KaminoFlashLoanAdapter::simulate_execution(&config, &callback_data).unwrap();
        
        // 验证利润计算: 1000 * 0.1% - 1000 * 0.05% = 1 - 0.5 = 0.5
        let expected_gross = config.amount / 1000; // 0.1%
        let expected_fee = config.amount * 5 / 10000; // 0.05%
        let expected_net = expected_gross - expected_fee;
        
        assert_eq!(profit, expected_net);
    }
}