//! # 多角套利引擎
//!
//! 支持跨多个池子的循环套利检测和路径优化

mod engine;
mod dp_engine;
mod bellman_ford_engine;

pub use engine::{
    ArbitrageEngine,
    ArbitragePath,
    SwapStep,
    TokenEdge,
    TokenGraph,
    EngineStats,
    tokens,
};

pub use dp_engine::{
    DpArbitrageEngine,
    DpEngineStats,
};

pub use bellman_ford_engine::{
    BellmanFordArbitrageEngine,
};

// 从 dex-instructions 导入交换指令构建器
pub use dex_{
    SwapInstructionBuilder,
    PumpSwapInstructionBuilder,
    pump::{create_buy_instruction, create_sell_instruction, constants as pump_constants}
};

// 从shared中导出基础类型
pub use shared::{ArbitrageResult, ArbitrageError, DexProtocol};
