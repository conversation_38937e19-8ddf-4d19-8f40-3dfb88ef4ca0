use dex_meteora::{MeteoraSwapInstruction, MeteoraSwapParams};
use shared::anchor_types::meteora::{LbPair, StaticParameters, VariableParameters, ProtocolFee, RewardInfo, Oracle};
use solana_sdk::{pubkey::Pubkey, signature::Keypair, signer::Signer};
use std::str::FromStr;

fn create_mock_lb_pair() -> LbPair {
    LbPair {
        parameters: StaticParameters {
            base_factor: 5000,
            filter_period: 10,
            decay_period: 600,
            reduction_factor: 5000,
            variable_fee_control: 40000000,
            max_volatility_accumulator: 350000,
            min_bin_id: -8388608,
            max_bin_id: 8388607,
            protocol_share: 2500,
            base_fee_power_factor: 8,
            padding: [0; 5],
        },
        v_parameters: VariableParameters {
            volatility_accumulator: 0,
            volatility_reference: 0,
            index_reference: 0,
            padding: [0; 4],
            last_update_timestamp: 1693123200,
            padding1: [0; 8],
        },
        bump_seed: [254],
        bin_step_seed: [25, 0],
        pair_type: 1,
        active_id: 8215,
        bin_step: 25,
        status: 1,
        require_base_factor_seed: 0,
        base_factor_seed: [0, 0],
        activation_type: 0,
        creator_pool_on_off_control: 0,
        token_x_mint: Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(), // USDC
        token_y_mint: Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(),  // SOL
        reserve_x: Pubkey::from_str("7BQGKzA4WaWZ4rzGcnK2VvZCfDjZ3JvKnvbwKEgFKvKR").unwrap(),
        reserve_y: Pubkey::from_str("5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1").unwrap(),
        padding1: [0; 32],
        protocol_fee: ProtocolFee {
            amount_x: 0,
            amount_y: 0,
        },
        reward_infos: [
            RewardInfo {
                mint: Pubkey::default(),
                vault: Pubkey::default(),
                funder: Pubkey::default(),
                reward_duration: 0,
                reward_duration_end: 0,
                reward_rate: 0,
                last_update_time: 0,
                cumulative_seconds_with_empty_liquidity_reward: 0,
            },
            RewardInfo {
                mint: Pubkey::default(),
                vault: Pubkey::default(),
                funder: Pubkey::default(),
                reward_duration: 0,
                reward_duration_end: 0,
                reward_rate: 0,
                last_update_time: 0,
                cumulative_seconds_with_empty_liquidity_reward: 0,
            },
        ],
        oracle: Pubkey::from_str("8nMT3YdDwV3JrEb9Ftb4o1yrcw1c3c7NJK1cLLPrRrU9").unwrap(),
        bin_array_bitmap: [0; 16],
        last_updated_at: 1693123200,
        padding2: [0; 32],
        pre_activation_swap_address: Pubkey::default(),
        base_key: Pubkey::default(),
        activation_point: 0,
        pre_activation_duration: 0,
        padding3: [0; 8],
        padding4: 0,
        creator: Pubkey::default(),
        token_mint_x_program_flag: 0,
        token_mint_y_program_flag: 0,
        reserved: [0; 22],
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Meteora DLMM 交换指令示例");
    println!("=======================");

    // 创建用户钱包
    let user = Keypair::new();
    println!("用户钱包地址: {}", user.pubkey());

    // 创建模拟的LB Pair
    let lb_pair = create_mock_lb_pair();
    println!("LB Pair Token X (USDC): {}", lb_pair.token_x_mint);
    println!("LB Pair Token Y (SOL): {}", lb_pair.token_y_mint);
    println!("当前活跃 Bin ID: {}", lb_pair.active_id);

    // 示例1: 用USDC买SOL (swap_for_y = true)
    println!("\n示例1: 用 100 USDC 买 SOL");
    let swap_params_buy = MeteoraSwapParams {
        lb_pair: Pubkey::from_str("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo")?,
        amount_in: 100_000_000, // 100 USDC (6 decimals)
        min_amount_out: 1_000_000_000, // 最少接受 1 SOL (9 decimals)
        swap_for_y: true, // 买Y代币 (SOL)
    };

    let buy_instruction = MeteoraSwapInstruction::build_swap_instruction(
        swap_params_buy,
        &lb_pair,
        user.pubkey(),
    )?;

    println!("交换指令程序ID: {}", buy_instruction.program_id);
    println!("交换指令账户数量: {}", buy_instruction.accounts.len());
    println!("交换指令数据长度: {} bytes", buy_instruction.data.len());

    // 示例2: 用SOL买USDC (swap_for_y = false)
    println!("\n示例2: 用 1 SOL 买 USDC");
    let swap_params_sell = MeteoraSwapParams {
        lb_pair: Pubkey::from_str("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo")?,
        amount_in: 1_000_000_000, // 1 SOL (9 decimals)
        min_amount_out: 50_000_000, // 最少接受 50 USDC (6 decimals)
        swap_for_y: false, // 买X代币 (USDC)
    };

    let sell_instruction = MeteoraSwapInstruction::build_swap_instruction(
        swap_params_sell,
        &lb_pair,
        user.pubkey(),
    )?;

    println!("交换指令程序ID: {}", sell_instruction.program_id);
    println!("交换指令账户数量: {}", sell_instruction.accounts.len());
    println!("交换指令数据长度: {} bytes", sell_instruction.data.len());

    // 显示重要的派生地址
    println!("\n派生地址信息:");
    let (event_authority, event_bump) = MeteoraSwapInstruction::derive_event_authority_pda();
    println!("Event Authority: {} (bump: {})", event_authority, event_bump);

    let lb_pair_address = Pubkey::from_str("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo")?;
    let (bitmap_extension, bitmap_bump) = MeteoraSwapInstruction::derive_bin_array_bitmap_extension(lb_pair_address);
    println!("Bitmap Extension: {} (bump: {})", bitmap_extension, bitmap_bump);

    let bin_array_index = (lb_pair.active_id as f64 / 70.0).ceil() as i64;
    let (bin_array, bin_bump) = MeteoraSwapInstruction::derive_bin_array_address(lb_pair_address, bin_array_index);
    println!("Bin Array (index: {}): {} (bump: {})", bin_array_index, bin_array, bin_bump);

    println!("\n✅ Meteora DLMM 交换指令示例完成！");
    println!("💡 这些指令可以被添加到交易中，在Solana区块链上执行交换操作。");

    Ok(())
}
